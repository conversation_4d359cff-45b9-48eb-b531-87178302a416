OCJSCanvas.vue:830 OCJSCanvas unmounted
GraphicsCanvas.vue:104 drawCanvas called with 30 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 30 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 30 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 30 commands
GraphicsCanvas.vue:176 drawCanvas completed
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  a = 50
  b = 30
  windowDepthFront = 12
  windowDepthBack = 8
  cW = 20 
  aV = 90 
    
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> iş<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 5   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  local debug = false
  
  if ((Y-2*b)/(X-2*a)<=1.5) then
    print ("Must have vertical aspect ratio.")
    return true
  end
  
  -- known parameters
  
  local point1 = {a, b}  
  local point2 = {a, Y-b}
  local point3 = {X-a, b}
  local point4 = {X-a, Y-b}
  local point5 = {a, Y/2}
  local point6 = {X-a, Y/2}
  local point7 = {X/2, Y-b}
  local point8 = {X/2, b}
  local point9 = {a+(X-2*a)/4, b+(Y-2*b)/2}
  local point10 = {a+3*(X-2*a)/4, b+(Y-2*b)/2}
  
  -- Calculate the unknown
  
  local bulge2 = G.bulge(point1, point10, point2)
  local radius = G.radius(point1, point2, bulge2)
  comment, intersection1, dummy = G.circleCircleIntersection(point2, radius, point1, radius)
  comment, dummy, intersection2 = G.circleCircleIntersection(point4, radius, point3, radius)
  comment, point12, point11 = G.circleCircleIntersection(intersection1, radius, intersection2, radius) 
  comment, dummy, intersection3 = G.circleLineIntersection(intersection1, radius, point5, point7) 
  local bulge3 = G.bulge(point2, intersection3, point11)
  comment, dummy, intersection4 = G.circleLineIntersection(intersection1, radius, point5, point8)
  local bulge4 = G.bulge(point12, intersection4, point1)
  
  -- Draw the final polylines
  
  G.setLayer("H_AciliV" .. aV .. "_Ic")
  G.setThickness(-windowDepthFront)
  
  bulge5 = G.bulge(point11, point10, point12)
  point2[4] = bulge3
  point11[4] = -bulge5
  point12[4] = bulge4
  G.polyline(point1, point2, point11, point12, point1)  -- left
  
  point3[4] = bulge4
  point12[4] = -bulge5
  point11[4] = bulge3
  G.polyline(point4, point3, point12, point11, point4)  -- right
  
  point2[4] = 0
  point4[4] = -bulge3
  point11[4] = -bulge3
  G.polyline(point2, point4, point11, point2)  -- top
  
  point3[4] = 0
  point1[4] = -bulge4 
  point12[4] = -bulge4
  G.polyline(point3, point1, point12, point3)  -- bottom
  
  G.setLayer("H_AciliV" .. aV .. "_Dis")
  G.setThickness(-windowDepthFront)
  point11[4] = bulge5
  point12[4] = bulge5
  G.polyline(point11, point12, point11)  -- middle
  
  
  if (debug) then
    G.circle(intersection1, radius)
    G.circle(intersection2, radius)
    G.polyline(point5, point7, point6, point8, point5, point6)
    G.line(point11, point2, -bulge3)
    G.line(point11, point12, bulge2)
    G.line(point12, point1, bulge4)
  end
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-cW/2, b-cW/2}, {X-a+cW/2, Y-b+cW/2})
  
  G.setLayer("K_Freze_" .. cW .. "mm_SF")
  G.line(point1, point2, bulge2)
  G.line(point4, point3, bulge2)
  
  return true
end

require "ADekoDebugMode"
luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
OCJSCanvas.vue:131 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:145 📊 Processing 30 draw commands
OCJSCanvas.vue:149 🔍 Parsed commands: {panelCommands: Array(8), topTools: Array(2), bottomTools: Array(2)}
OCJSCanvas.vue:156 🔧 Panel commands found: 8
OCJSCanvas.vue:157 🔧 Top tools found: 2
OCJSCanvas.vue:158 🔧 Bottom tools found: 2
OCJSCanvas.vue:161 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:163 📏 Extracted door parameters: {width: 500, height: 700, thickness: 18, cornerRadius: 5, offsetX: 40, …}
OCJSCanvas.vue:175 Door parameters: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: 0.005, offsetX: 40, …}
OCJSCanvas.vue:176 Door size in mm: W=500 H=700 T=18
ocjsWorker.ts:1426 🔧 Worker received message: ping
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1437 🏓 Worker ping received
ocjsWorker.ts:1486 ✅ Worker completed: ping
OCJSCanvas.vue:735 OCJSCanvas mounted
ocjsService.ts:209 ✅ Worker ping successful in 7ms
OCJSCanvas.vue:186 ✅ Worker is responsive, proceeding with processing
OCJSCanvas.vue:213 ⚙️ Sending to worker: {doorParamsMeters: {…}, topTools: Array(2), bottomTools: Array(2)}
ocjsService.ts:242 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:244 📐 Creating door body with params: {width: 0.5, height: 0.7, thickness: 0.018, cornerRadius: 0.005, offsetX: 40, …}
OCJSCanvas.vue:222 📊 Progress: 🏗️ Creating door body geometry... (0s)
ocjsWorker.ts:1426 🔧 Worker received message: createDoorBody
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1441 🚪 Creating door body...
ocjsWorker.ts:117 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:121 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:128 🚪 Door body positioned: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[0, -0.018]
ocjsWorker.ts:133 ✅ Door body cached with ID: door_1752351695101
ocjsWorker.ts:1486 ✅ Worker completed: createDoorBody
ocjsService.ts:249 ✅ Door body created successfully
ocjsService.ts:256 🔍 All tool operations: (4) ['90° V-Bit (conical, ⌀20mm) - 2 commands', '90° V-Bit (conical, ⌀20mm) - 14 commands', '20mm End Mill (cylindrical, ⌀20mm) - 4 commands', '20mm End Mill (cylindrical, ⌀20mm) - 2 commands']
OCJSCanvas.vue:222 📊 Progress: 🔧 Processing 4 tool operations... (0s)
ocjsService.ts:260 🔧 Creating positioned tool shapes for 4 tool operations...
OCJSCanvas.vue:222 📊 Progress: 🔧 Processing tool 1/4: 90° V-Bit (2 operations) (0s)
ocjsService.ts:269 🔧 Processing 90° V-Bit with 2 commands
ocjsWorker.ts:1426 🔧 Worker received message: createPositionedToolShapes
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1465 🔧 Creating positioned tool shapes...
ocjsWorker.ts:327 🔧 Tool 90° V-Bit: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:329 🔧 Creating 2 positioned conical tool shapes for 90° V-Bit
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Dis → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(69.333, 446)mm, radius=320.667mm, angles=406.516°-313.484°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Dis → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(510.667, 446)mm, radius=320.667mm, angles=226.516°-133.484°, operation=profiling
ocjsWorker.ts:1073 ✅ Created 2 positioned tool shapes
ocjsWorker.ts:1486 ✅ Worker completed: createPositionedToolShapes
ocjsService.ts:287 ✅ Created 2 positioned shapes for 90° V-Bit
OCJSCanvas.vue:222 📊 Progress: 🔧 Processing tool 2/4: 90° V-Bit (14 operations) (0s)
ocjsService.ts:269 🔧 Processing 90° V-Bit with 14 commands
ocjsWorker.ts:1426 🔧 Worker received message: createPositionedToolShapes
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1465 🔧 Creating positioned tool shapes...
ocjsWorker.ts:327 🔧 Tool 90° V-Bit: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:329 🔧 Creating 14 positioned conical tool shapes for 90° V-Bit
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(69.333, 446)mm, radius=320.667mm, angles=86.305°-46.516°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(510.667, 446)mm, radius=320.667mm, angles=133.484°-226.516°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(69.333, 446)mm, radius=320.667mm, angles=313.484°-273.695°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(510.667, 446)mm, radius=320.667mm, angles=266.305°-226.516°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(69.333, 446)mm, radius=320.667mm, angles=-46.516°-46.516°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(510.667, 446)mm, radius=320.667mm, angles=133.484°-93.695°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(510.667, 446)mm, radius=320.667mm, angles=93.695°-133.484°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(69.333, 446)mm, radius=320.667mm, angles=46.516°-86.305°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(69.333, 446)mm, radius=320.667mm, angles=273.695°-313.484°, operation=profiling
ocjsWorker.ts:988 🔧 Arc Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:1039 🔧 ARC POCKET: Creating circular pocket at arc center
ocjsWorker.ts:1055 🔧 Arc operation: center=(510.667, 446)mm, radius=320.667mm, angles=226.516°-266.305°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:543 🔧 LINE POCKET: Applying rotation 90.0°
ocjsWorker.ts:563 🔧 Line operation: center=(90, 446)mm, length=640.0mm, angle=90.0°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:543 🔧 LINE POCKET: Applying rotation -90.0°
ocjsWorker.ts:563 🔧 Line operation: center=(490, 446)mm, length=640.0mm, angle=-90.0°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:563 🔧 Line operation: center=(290, 766)mm, length=400.0mm, angle=0.0°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_AciliV90_Ic → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:543 🔧 LINE POCKET: Applying rotation 180.0°
ocjsWorker.ts:563 🔧 Line operation: center=(290, 126)mm, length=400.0mm, angle=180.0°, operation=profiling
ocjsWorker.ts:1073 ✅ Created 14 positioned tool shapes
ocjsWorker.ts:1486 ✅ Worker completed: createPositionedToolShapes
ocjsService.ts:287 ✅ Created 14 positioned shapes for 90° V-Bit
OCJSCanvas.vue:222 📊 Progress: 🔧 Processing tool 3/4: 20mm End Mill (4 operations) (0s)
ocjsService.ts:269 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:1426 🔧 Worker received message: createPositionedToolShapes
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1465 🔧 Creating positioned tool shapes...
ocjsWorker.ts:327 🔧 Tool 20mm End Mill: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:329 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:421 🔧 Line Layer: H_Freze20mm_Ic_SF → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:543 🔧 LINE POCKET: Applying rotation 180.0°
ocjsWorker.ts:563 🔧 Line operation: center=(886, 776)mm, length=420.0mm, angle=180.0°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_Freze20mm_Ic_SF → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:543 🔧 LINE POCKET: Applying rotation -90.0°
ocjsWorker.ts:563 🔧 Line operation: center=(676, 446)mm, length=660.0mm, angle=-90.0°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_Freze20mm_Ic_SF → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:543 🔧 LINE POCKET: Applying rotation 90.0°
ocjsWorker.ts:563 🔧 Line operation: center=(1096, 446)mm, length=660.0mm, angle=90.0°, operation=profiling
ocjsWorker.ts:421 🔧 Line Layer: H_Freze20mm_Ic_SF → Operation: profiling
ocjsWorker.ts:520 🔧 LINE POCKET: Creating elongated rectangular pocket
ocjsWorker.ts:563 🔧 Line operation: center=(886, 116)mm, length=420.0mm, angle=0.0°, operation=profiling
ocjsWorker.ts:1073 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:1486 ✅ Worker completed: createPositionedToolShapes
ocjsService.ts:287 ✅ Created 4 positioned shapes for 20mm End Mill
OCJSCanvas.vue:222 📊 Progress: 🔧 Processing tool 4/4: 20mm End Mill (2 operations) (0s)
ocjsService.ts:269 🔧 Processing 20mm End Mill with 2 commands
ocjsWorker.ts:1426 🔧 Worker received message: createPositionedToolShapes
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1465 🔧 Creating positioned tool shapes...
ocjsWorker.ts:327 🔧 Tool 20mm End Mill: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:329 🔧 Creating 2 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:988 🔧 Arc Layer: K_Freze_20mm_SF → Operation: grooving
ocjsWorker.ts:1001 🔧 ARC GROOVE: Creating arc-shaped groove
ocjsWorker.ts:1055 🔧 Arc operation: center=(665.333, 446)mm, radius=320.667mm, angles=-86.305°-86.305°, operation=grooving
ocjsWorker.ts:988 🔧 Arc Layer: K_Freze_20mm_SF → Operation: grooving
ocjsWorker.ts:1001 🔧 ARC GROOVE: Creating arc-shaped groove
ocjsWorker.ts:1055 🔧 Arc operation: center=(1106.667, 446)mm, radius=320.667mm, angles=93.695°-266.305°, operation=grooving
ocjsWorker.ts:1073 ✅ Created 2 positioned tool shapes
ocjsWorker.ts:1486 ✅ Worker completed: createPositionedToolShapes
ocjsService.ts:287 ✅ Created 2 positioned shapes for 20mm End Mill
OCJSCanvas.vue:222 📊 Progress: 🔧 Performing sweep operations with 22 tool shapes... (0s)
ocjsService.ts:299 🔧 Performing sweep operations with 22 positioned tool shapes...
ocjsWorker.ts:1426 🔧 Worker received message: performSweepOperation
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1449 🔄 Performing sweep operation...
ocjsWorker.ts:1239 🔧 Starting sweep operation: subtract
ocjsWorker.ts:1258 🔧 Processing 22 tool geometries
ocjsWorker.ts:1261 🔍 Starting CSG operations on door body
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_0_1752351695105_2qgex6zyp
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 0 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:1304 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_1_1752351695106_9t8rmouxh
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 1 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:1304 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_0_1752351695109_yt8kf3nzv
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 2 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:1304 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_1_1752351695109_bilb75k8b
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 3 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:1304 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_2_1752351695110_n1hf31lb6
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 4 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 4...
ocjsWorker.ts:1304 ✅ Tool 4 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_3_1752351695111_j4bitufk0
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 5 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 5...
ocjsWorker.ts:1304 ✅ Tool 5 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_4_1752351695112_54n6hzcrk
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 6 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 6...
ocjsWorker.ts:1304 ✅ Tool 6 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_5_1752351695113_1x2w424lk
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 7 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 7...
ocjsWorker.ts:1304 ✅ Tool 7 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_6_1752351695114_dypfofkl4
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 8 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 8...
ocjsWorker.ts:1304 ✅ Tool 8 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_7_1752351695115_75q3717bq
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 9 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 9...
ocjsWorker.ts:1304 ✅ Tool 9 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_8_1752351695116_afc427odq
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 10 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 10...
ocjsWorker.ts:1304 ✅ Tool 10 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_9_1752351695117_szi49ejlu
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 11 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 11...
ocjsWorker.ts:1304 ✅ Tool 11 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_10_1752351695118_4shx2os7c
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 12 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 12...
ocjsWorker.ts:1304 ✅ Tool 12 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_11_1752351695119_94rl3oe6n
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 13 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 13...
ocjsWorker.ts:1304 ✅ Tool 13 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_12_1752351695119_ncvwp8xfy
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 14 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 14...
ocjsWorker.ts:1304 ✅ Tool 14 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_90° V-Bit_13_1752351695120_wgds9b4o6
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 15 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 15...
ocjsWorker.ts:1304 ✅ Tool 15 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_20mm End Mill_0_1752351695122_vnu0ur3e2
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 16 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 16...
ocjsWorker.ts:1304 ✅ Tool 16 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_20mm End Mill_1_1752351695123_chdiqm920
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 17 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 17...
ocjsWorker.ts:1304 ✅ Tool 17 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_20mm End Mill_2_1752351695124_xujvg6g7i
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 18 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 18...
ocjsWorker.ts:1304 ✅ Tool 18 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_20mm End Mill_3_1752351695124_kiy6zlm95
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 19 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 19...
ocjsWorker.ts:1304 ✅ Tool 19 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_20mm End Mill_0_1752351695174_23pmr9wks
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 20 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 20...
ocjsWorker.ts:1304 ✅ Tool 20 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1272 🔍 Looking for tool shape ID: positioned_tool_20mm End Mill_1_1752351695206_6x00i93bz
ocjsWorker.ts:1273 🔍 Found in toolCache: true
ocjsWorker.ts:1274 🔍 Found in shapeCache: false
ocjsWorker.ts:1291 ✅ Found tool shape 21 for operation
ocjsWorker.ts:1294 🔧 Attempting to subtract tool 21...
ocjsWorker.ts:1304 ✅ Tool 21 subtracted successfully
ocjsWorker.ts:1308 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:1340 ✅ Sweep operation completed, result cached with ID: result_1752351719531
ocjsWorker.ts:1341 📊 Successfully processed 22 out of 22 tools
ocjsWorker.ts:1486 ✅ Worker completed: performSweepOperation
ocjsService.ts:306 ✅ Sweep operations completed: result_1752351719531
OCJSCanvas.vue:222 📊 Progress: 📦 Exporting final result to GLB... (24s)
ocjsService.ts:310 🔧 Exporting final result to GLB...
ocjsWorker.ts:1426 🔧 Worker received message: exportGLB
ocjsWorker.ts:1429 🔧 Ensuring OpenCascade.js is initialized...
ocjsWorker.ts:1431 ✅ OpenCascade.js ready, proceeding with operation...
ocjsWorker.ts:1453 📦 Exporting to GLB...
ocjsWorker.ts:1362 🔧 Exporting to GLB...
ocjsWorker.ts:1387 🔍 Exporting final shape to GLB format
ocjsWorker.ts:1413 ✅ GLB export completed, size: 930800 bytes
ocjsWorker.ts:1486 ✅ Worker completed: exportGLB
ocjsService.ts:312 ✅ Final GLB exported, size: 930800 bytes
OCJSCanvas.vue:262 ✅ Worker processing completed, GLB data size: 930800
OCJSCanvas.vue:276 Model URL created: blob:http://localhost:1420/61d14127-8f6d-4dbf-b3c8-b51599f1563c
OCJSCanvas.vue:351 ✅ Initializing Three.js scene
OCJSCanvas.vue:352 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:353 📐 Container dimensions: {width: 1920, height: 0, offsetWidth: 1920, offsetHeight: 0}
OCJSCanvas.vue:372 Container dimensions: {width: 1920, height: 600, aspect: 3.2}
OCJSCanvas.vue:388 Renderer created with size: 1920 x 600
OCJSCanvas.vue:389 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:390 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:425 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:437 🔄 Loading GLB model: blob:http://localhost:1420/61d14127-8f6d-4dbf-b3c8-b51599f1563c
OCJSCanvas.vue:510 Loading progress: 100%
OCJSCanvas.vue:444 GLB model loaded successfully
OCJSCanvas.vue:469 Model bounding box: {center: _Vector3, size: _Vector3, min: _Vector3, max: _Vector3}
OCJSCanvas.vue:481 Max dimension: 1.3923340000000044
OCJSCanvas.vue:485 Camera distance: 4.177002000000013
OCJSCanvas.vue:497 Model centered and camera adjusted
OCJSCanvas.vue:498 Camera position: _Vector3 {x: 4.177002000000012, y: 3.13275150000001, z: 4.177002000000013}
OCJSCanvas.vue:499 Model position: _Vector3 {x: -0.4461669999999977, y: 0, z: 0.01}
