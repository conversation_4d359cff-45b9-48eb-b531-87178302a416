// Import OpenCascade.js dynamically to avoid issues with WASM loading
// FIXED VERSION 2.9 - Size Debug - Cache Buster: 2025-07-05-22:35
import type { CNCTool, DrawCommand } from '../types'

// Worker message types
export interface OCJSWorkerMessage {
  id: string
  type: 'ping' | 'createDoorBody' | 'createToolGeometry' | 'performSweepOperation' | 'exportGLB' | 'createToolBRep' | 'createAllToolBReps' | 'createPositionedToolShapes' | 'createSimpleBoxGLB' | 'testPolylineSweep'
  data: any
}

export interface OCJSWorkerResponse {
  id: string
  type: 'success' | 'error'
  data?: any
  error?: string
}

// Door body creation parameters
export interface DoorBodyParams {
  width: number
  height: number
  thickness: number
  cornerRadius?: number
  offsetX?: number
  offsetY?: number
}

// Tool geometry creation parameters
export interface ToolGeometryParams {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
  doorWidth?: number
  doorHeight?: number
}

// Sweep operation parameters
export interface SweepOperationParams {
  doorBodyShape: any // OC shape reference
  toolGeometries: any[] // Array of OC tool shapes
  operation: 'subtract' | 'union' | 'intersect'
}

// Tool BRep creation parameters
export interface ToolBRepParams {
  tool: CNCTool
  height?: number
  includeGLB?: boolean
}

// All tools BRep creation parameters
export interface AllToolBRepsParams {
  tools: CNCTool[]
  height?: number
  includeGLB?: boolean
}

// Test polyline sweep parameters
export interface TestPolylineSweepParams {
  polylinePoints: { x: number; y: number; z: number }[]
  profileShape: string // Shape ID
  doorBodyShape: string // Shape ID
}

let oc: any = null
let isInitialized = false

// Shape cache for storing OpenCascade shapes between operations
const shapeCache = new Map<string, any>()
const toolCache = new Map<string, any>()



// Initialize OpenCascade.js
async function initializeOC() {
  if (!isInitialized) {
    try {
      console.log('🔧 Starting OpenCascade.js initialization...')
      
      // Add timeout for initialization
      const initTimeout = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('OpenCascade.js initialization timeout after 30 seconds'))
        }, 30000)
      })
      
      const initPromise = (async () => {
        // Dynamic import to handle WASM loading properly
        const { default: initOpenCascade } = await import('opencascade.js')
        console.log('🔧 OpenCascade.js module loaded, initializing WASM...')
        oc = await initOpenCascade()
        console.log('✅ OpenCascade.js WASM initialized successfully')
        return oc
      })()
      
      oc = await Promise.race([initPromise, initTimeout])
      isInitialized = true
      console.log('✅ OpenCascade.js initialized in worker')
    } catch (error) {
      console.error('❌ Failed to initialize OpenCascade.js:', error)
      isInitialized = false
      throw error
    }
  }
  return oc
}

// Create door body from PANEL layer data
function createDoorBody(params: DoorBodyParams): any {
  const { width, height, thickness, cornerRadius: _cornerRadius = 0 } = params

  try {
    // For now, create a simple rectangular door body to avoid OpenCascade.js constructor issues
    // TODO: Add rounded corners support once the correct API is determined
    console.log('Creating box with dimensions:', width, height, thickness)

    // Create a simple box
    const box = new oc.BRepPrimAPI_MakeBox_2(width, height, thickness)
    console.log(`🚪 Door body dimensions: W=${width}m, H=${height}m, T=${thickness}m`)

    // Position the box: centered in X,Y but from Z=0 down to Z=-thickness
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(-width/2, -height/2, -thickness))
    const loc = new oc.TopLoc_Location_2(tf)
    const positionedBox = box.Shape().Moved(loc, false)
    console.log(`🚪 Door body positioned: X=[-${width/2}, ${width/2}], Y=[-${height/2}, ${height/2}], Z=[0, -${thickness}]`)

    // Cache the door body shape
    const shapeId = `door_${Date.now()}`
    shapeCache.set(shapeId, positionedBox)
    console.log('✅ Door body cached with ID:', shapeId)

    return {
      success: true,
      shapeId: shapeId,
      dimensions: { width, height, thickness }
    }
  } catch (error) {
    console.error('Error creating door body:', error)
    throw error
  }
}

// Create tool geometry based on tool type and commands
function createToolGeometry(params: ToolGeometryParams): any {
  const { tool, commands, depth, isBottomFace, doorWidth, doorHeight } = params
  
  try {
    const toolGeometries: any[] = []
    
    commands.forEach(command => {
      let toolShape: any = null
      
      if (tool.shape === 'cylindrical') {
        // Create cylindrical tool
        const radius = tool.diameter / 2
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, depth)
        toolShape = cylinder.Shape()
      } else if (tool.shape === 'conical') {
        // Create conical tool (V-bit)
        const topRadius = tool.diameter / 2
        const bottomRadius = 0.1 // Small tip radius
        const cone = new oc.BRepPrimAPI_MakeCone_1(bottomRadius, topRadius, depth)
        toolShape = cone.Shape()
      } else if (tool.shape === 'ballnose') {
        // Create ballnose tool (hemisphere + cylinder)
        const radius = tool.diameter / 2
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, depth - radius)
        const sphere = new oc.BRepPrimAPI_MakeSphere_1(radius)
        
        // Position sphere at bottom of cylinder
        const tf = new oc.gp_Trsf_1()
        tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -radius))
        const loc = new oc.TopLoc_Location_2(tf)
        const movedSphere = sphere.Shape().Moved(loc, false)
        
        // Fuse cylinder and sphere
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          cylinder.Shape(),
          movedSphere,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        toolShape = fuse.Shape()
      }
      
      if (toolShape) {
        // Position tool based on command coordinates
        const tf = new oc.gp_Trsf_1()
        
        if (command.command_type === 'line') {
          // Convert from Lua coordinate system (0 to doorWidth/doorHeight) to centered coordinate system
          const midX = (command.x1 + command.x2) / 2 - (doorWidth || 0) / 2
          const midZ = (command.y1 + command.y2) / 2 - (doorHeight || 0) / 2
          const posY = isBottomFace ? -depth : 0
          console.log(`🔧 Positioning line tool at: X=${midX}, Y=${posY}, Z=${midZ} (converted from Lua coords)`)
          tf.SetTranslation_1(new oc.gp_Vec_4(midX, posY, midZ))
        } else if (command.command_type === 'circle') {
          const posY = isBottomFace ? -depth : 0
          // Convert from Lua coordinate system to centered coordinate system
          const centerX = command.x1 - (doorWidth || 0) / 2
          const centerZ = command.y1 - (doorHeight || 0) / 2
          console.log(`🔧 Positioning circle tool at: X=${centerX}, Y=${posY}, Z=${centerZ} (converted from Lua coords)`)
          tf.SetTranslation_1(new oc.gp_Vec_4(centerX, posY, centerZ))
        }
        
        const loc = new oc.TopLoc_Location_2(tf)
        const positionedTool = toolShape.Moved(loc, false)
        toolGeometries.push(positionedTool)
      }
    })
    
    // Return serializable data instead of OpenCascade objects
    return {
      count: toolGeometries.length,
      success: true,
      // Don't return the actual geometries as they can't be serialized
      // Store them in a global cache for later use
      geometryIds: toolGeometries.map((_, index) => `tool_${Date.now()}_${index}`)
    }
  } catch (error) {
    console.error('Error creating tool geometry:', error)
    throw error
  }
}

// Helper function to determine operation type from layer name
function determineOperationFromLayerName(layerName: string): string {
  const layer = layerName.toLowerCase()

  if (layer.includes('drill') || layer.includes('delme')) {
    return 'drilling'
  } else if (layer.includes('pocket') || layer.includes('cep')) {
    return 'pocketing'
  } else if (layer.includes('k_') || layer.includes('groove') || layer.includes('kanal')) {
    return 'grooving'
  } else if (layer.includes('h_') || layer.includes('contour')) {
    return 'profiling'
  } else if (layer.includes('v') && (layer.includes('acili') || layer.includes('chamfer'))) {
    return 'chamfering'
  } else if (layer.includes('radius') || layer.includes('fillet')) {
    return 'filleting'
  }

  return 'profiling' // Default operation
}

// Helper function to create circular groove shape (annular cylinder for surface groove)
function createCircularGrooveShape(centerX: number, centerY: number, grooveRadius: number, toolRadius: number, height: number): any {
  try {
    // Create an annular cylinder (ring) that represents the groove cut
    // This creates a cylindrical groove on the surface, not a torus
    const outerRadius = grooveRadius + toolRadius / 2
    const innerRadius = grooveRadius - toolRadius / 2

    // Ensure inner radius is positive
    const actualInnerRadius = Math.max(innerRadius, toolRadius / 4)

    console.log(`🔧 Creating groove: outer=${outerRadius*1000}mm, inner=${actualInnerRadius*1000}mm, height=${height*1000}mm`)

    // Create outer cylinder
    const outerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(outerRadius, height)

    // Create inner cylinder to subtract
    const innerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(actualInnerRadius, height)

    // Subtract inner from outer to create annular groove
    const cut = new oc.BRepAlgoAPI_Cut_3(
      outerCylinder.Shape(),
      innerCylinder.Shape(),
      new oc.Message_ProgressRange_1()
    )
    cut.Build(new oc.Message_ProgressRange_1())

    // Position the groove at the correct location
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)

    return cut.Shape().Moved(loc, false)
  } catch (error) {
    console.error('Error creating groove shape:', error)
    throw new Error(`Failed to create circular groove shape: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Helper function to create pocket shape from spiral toolpath
function createPocketShape(centerX: number, centerY: number, circleRadius: number, toolRadius: number, height: number): any {
  try {
    // For now, create a simple cylinder that represents the pocketed area
    // TODO: Implement proper spiral toolpath shape generation
    const pocketRadius = circleRadius - toolRadius / 2
    const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(pocketRadius, height)

    // Position the cylinder at the center
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)

    return cylinder.Shape().Moved(loc, false)
  } catch (error) {
    console.error('Error creating pocket shape:', error)
    throw new Error(`Failed to create pocket shape: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Create positioned tool shapes for each command
async function createPositionedToolShapes(params: {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
  doorWidth: number
  doorHeight: number
  doorOffsetX?: number
  doorOffsetY?: number
}): Promise<any> {
  const { tool, commands, depth, isBottomFace, doorWidth, doorHeight, doorOffsetX = 0, doorOffsetY = 0 } = params

  try {
    const positionedShapes: any[] = []
    const shapeIds: string[] = []
    // Convert tool diameter from mm to meters
    const radius = (tool.diameter / 1000) / 2
    console.log(`🔧 Tool ${tool.name}: diameter=${tool.diameter}mm, radius=${radius}m (${radius*1000}mm)`)

    console.log(`🔧 Creating ${commands.length} positioned ${tool.shape} tool shapes for ${tool.name}`)

    for (let index = 0; index < commands.length; index++) {
      const command = commands[index]
      let toolShape: any = null

      // Create base tool shape with appropriate height based on operation
      const doorThickness = 0.02 // 20mm door thickness in meters

      // Determine operation type first to set correct tool height
      const layerName = command.layer_name || tool.name || ''
      const operation = determineOperationFromLayerName(layerName)

      // Set tool height based on operation type
      let toolHeight: number
      if (operation === 'drilling') {
        toolHeight = depth // Use actual drilling depth for drilling operations
      } else {
        toolHeight = Math.max(depth, doorThickness * 1.5) // Ensure tool is tall enough for other operations
      }

      switch (tool.shape) {
        case 'cylindrical':
          const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, toolHeight)
          toolShape = cylinder.Shape()
          break

        case 'conical':
          const conicalTool = tool as any
          const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
          const cone = new oc.BRepPrimAPI_MakeCone_1(tipRadius, radius, toolHeight)
          toolShape = cone.Shape()
          break

        case 'ballnose':
          const ballnoseTool = tool as any
          const ballRadius = ballnoseTool.ballRadius || radius
          const cylHeight = Math.max(toolHeight - ballRadius, 0)
          const ballCylinder = new oc.BRepPrimAPI_MakeCylinder_1(ballRadius, cylHeight)
          const hemisphere = new oc.BRepPrimAPI_MakeSphere_1(ballRadius)

          // Position hemisphere at bottom
          const tf = new oc.gp_Trsf_1()
          tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -ballRadius))
          const loc = new oc.TopLoc_Location_2(tf)
          const movedHemisphere = hemisphere.Shape().Moved(loc, false)

          // Fuse cylinder and hemisphere
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            ballCylinder.Shape(),
            movedHemisphere,
            new oc.Message_ProgressRange_1()
          )
          fuse.Build(new oc.Message_ProgressRange_1())
          toolShape = fuse.Shape()
          break

        default:
          console.warn(`Unknown tool shape: ${tool.shape}`)
          return
      }

      if (toolShape) {
        // Position tool based on command coordinates
        const positionTf = new oc.gp_Trsf_1()

        if (command.command_type === 'line') {
          // Handle line operations - different operations based on layer name
          const x1_mm = command.x1 || 0
          const y1_mm = command.y1 || 0
          const x2_mm = command.x2 || 0
          const y2_mm = command.y2 || 0

          // Calculate line center and dimensions
          const midX_mm = (x1_mm + x2_mm) / 2
          const midY_mm = (y1_mm + y2_mm) / 2
          const lineLength_mm = Math.sqrt(Math.pow(x2_mm - x1_mm, 2) + Math.pow(y2_mm - y1_mm, 2))
          const lineAngle = Math.atan2(y2_mm - y1_mm, x2_mm - x1_mm)

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const midX = ((midX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const midY = ((midY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Determine operation type from layer name
          const layerName = command.layer_name || tool.name || ''
          const operation = determineOperationFromLayerName(layerName)
          const toolRadius = radius // Tool radius in meters

          console.log(`🔧 Line Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling') {
            // LINE DRILLING: Use cylindrical tool at line center (like a hole at midpoint)
            console.log(`🔧 LINE DRILLING: Using cylindrical tool at line center`)
            // Keep the original cylindrical toolShape (already created above)
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // LINE GROOVE: Use real sweep operation (BRepOffsetAPI_MakePipeShell)
            console.log(`🔧 LINE GROOVE: Creating groove using real sweep operation`)
            const startTime = performance.now()

            const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

            try {
              // Create line path as 3D points
              const linePoints3D = [
                {
                  x: midX - (x2_mm - x1_mm) / 2000, // Start point in meters
                  y: midY - (y2_mm - y1_mm) / 2000,
                  z: isBottomFace ? (-doorThickness + grooveDepth/2) : (0 - grooveDepth/2)
                },
                {
                  x: midX + (x2_mm - x1_mm) / 2000, // End point in meters
                  y: midY + (y2_mm - y1_mm) / 2000,
                  z: isBottomFace ? (-doorThickness + grooveDepth/2) : (0 - grooveDepth/2)
                }
              ]

              console.log(`🔧 LINE GROOVE: Creating sweep path from (${linePoints3D[0].x.toFixed(3)}, ${linePoints3D[0].y.toFixed(3)}) to (${linePoints3D[1].x.toFixed(3)}, ${linePoints3D[1].y.toFixed(3)})`)

              // Create circular profile for the groove (tool cross-section)
              const grooveRadius = toolRadius * 0.8 // Slightly smaller than tool for realistic groove
              const profileCircle = new oc.BRepPrimAPI_MakeCylinder_1(grooveRadius, grooveDepth)
              const profileShape = profileCircle.Shape()

              // Use the new real sweep function
              const sweptGroove = await createRealSweepFromPolyline(oc, linePoints3D, profileShape)

              if (sweptGroove && !sweptGroove.IsNull()) {
                toolShape = sweptGroove
                console.log(`✅ LINE GROOVE: Real sweep completed in ${(performance.now() - startTime).toFixed(1)}ms`)

                // Position transformation is identity since sweep already positioned the shape
                positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
              } else {
                throw new Error('Real sweep operation failed')
              }

            } catch (sweepError) {
              console.error(`❌ LINE GROOVE: Real sweep failed, falling back to legacy method:`, sweepError)

              // Fallback to legacy box approach
              const lineLength = lineLength_mm / 1000  // Convert to meters
              const grooveWidth = toolRadius * 2 // Groove width based on tool diameter

              // Create elongated box for the groove, centered at origin
              const grooveBox = new oc.BRepPrimAPI_MakeBox_2(lineLength, grooveWidth, grooveDepth)
              toolShape = grooveBox.Shape()

              // Apply rotation and translation together
              const grooveZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth

              // Create combined transformation: first center the box, then rotate, then translate
              const combinedTf = new oc.gp_Trsf_1()

              // First, center the box at origin (box is created from 0,0,0)
              combinedTf.SetTranslation_1(new oc.gp_Vec_4(-lineLength/2, -grooveWidth/2, 0))

              // Rotate around origin if needed
              if (Math.abs(lineAngle) > 0.01) {
                console.log(`🔧 LINE GROOVE: Applying rotation ${(lineAngle * 180 / Math.PI).toFixed(1)}°`)
                const rotationTf = new oc.gp_Trsf_1()
                const rotationAxis = new oc.gp_Ax1_2(new oc.gp_Pnt_1(), new oc.gp_Dir_4(0, 0, 1)) // Z-axis
                rotationTf.SetRotation_1(rotationAxis, lineAngle)
                combinedTf.PreMultiply(rotationTf)
              }

              // Apply final translation to move to target position
              const translateTf = new oc.gp_Trsf_1()
              translateTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, grooveZ))
              combinedTf.PreMultiply(translateTf)

              // Apply the combined transformation to the shape
              const combinedLoc = new oc.TopLoc_Location_2(combinedTf)
              toolShape = toolShape.Moved(combinedLoc, false)

              // Position transformation is identity since we already positioned the shape
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
            }

          } else {
            // LINE POCKET: Create elongated rectangular pocket along line
            console.log(`🔧 LINE POCKET: Creating elongated rectangular pocket`)

            const lineLength = lineLength_mm / 1000  // Convert to meters
            const pocketWidth = toolRadius * 3 // Wider pocket
            const pocketDepth = Math.min(depth, lineLength / 4) // Reasonable pocket depth

            // Create elongated box for the pocket, centered at origin
            const pocketBox = new oc.BRepPrimAPI_MakeBox_2(lineLength, pocketWidth, pocketDepth)
            toolShape = pocketBox.Shape()

            // Apply rotation and translation together
            const pocketZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
            
            // Create combined transformation: first center the box, then rotate, then translate
            const combinedTf = new oc.gp_Trsf_1()
            
            // First, center the box at origin (box is created from 0,0,0)
            combinedTf.SetTranslation_1(new oc.gp_Vec_4(-lineLength/2, -pocketWidth/2, 0))
            
            // Rotate around origin if needed
            if (Math.abs(lineAngle) > 0.01) {
              console.log(`🔧 LINE POCKET: Applying rotation ${(lineAngle * 180 / Math.PI).toFixed(1)}°`)
              const rotationTf = new oc.gp_Trsf_1()
              const rotationAxis = new oc.gp_Ax1_2(new oc.gp_Pnt_1(), new oc.gp_Dir_4(0, 0, 1)) // Z-axis
              rotationTf.SetRotation_1(rotationAxis, lineAngle)
              combinedTf.PreMultiply(rotationTf)
            }
            
            // Apply final translation to move to target position
            const translateTf = new oc.gp_Trsf_1()
            translateTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, pocketZ))
            combinedTf.PreMultiply(translateTf)
            
            // Apply the combined transformation to the shape
            const combinedLoc = new oc.TopLoc_Location_2(combinedTf)
            toolShape = toolShape.Moved(combinedLoc, false)
            
            // Position transformation is identity since we already positioned the shape
            positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
          }

          console.log(`🔧 Line operation: center=(${midX_mm}, ${midY_mm})mm, length=${lineLength_mm.toFixed(1)}mm, angle=${(lineAngle * 180 / Math.PI).toFixed(1)}°, operation=${operation}`)
        } else if (command.command_type === 'circle') {
          // Convert from Lua coordinate system (mm) to centered coordinate system (meters)
          const centerX_mm = command.x1
          const centerY_mm = command.y1
          const circleRadius_mm = command.radius || 0 // Circle radius from DrawCommand

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000
          const circleRadius = circleRadius_mm / 1000 // Convert to meters

          // Position tool to cut from the surface into the door
          const doorThickness = 0.02 // 20mm door thickness in meters
          const toolHeight = Math.max(depth, doorThickness * 1.5)

          // For bottom face operations, position tool to start at bottom surface and extend upward
          // For top face operations, position tool to start at top surface and extend downward
          const posZ = isBottomFace ?
            (-doorThickness + toolHeight/2) : // Bottom face: start at bottom surface, center tool
            (0 - toolHeight/2)    // Top face: start at top surface, center tool

          const toolRadius = radius // Tool radius in meters
          console.log(`🔧 Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling' || circleRadius <= 0) {
            // DRILLING: Single point operation at circle center
            // Use actual drilling depth, not toolHeight
            const actualDrillDepth = depth // Use the specified depth (10mm = 0.01m)
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - actualDrillDepth) // Top face: start at top surface (Z=0), extend down by drill depth only
            console.log(`🔧 DRILLING: Positioning drill tool ${index} at center: X=${centerX.toFixed(4)}m, Y=${centerY.toFixed(4)}m, Z=${drillZ.toFixed(4)}m (depth=${actualDrillDepth*1000}mm, doorThickness=${doorThickness*1000}mm, isBottomFace=${isBottomFace})`)
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // GROOVE: Create circular groove shape around circle perimeter (borderline)
            console.log(`🔧 GROOVE: Creating groove toolpath around circle perimeter (radius=${circleRadius_mm}mm, tool=${toolRadius*1000}mm)`)

            if (circleRadius > toolRadius) {
              // Create shallow circular groove shape (surface cut only)
              const grooveRadius = circleRadius
              const grooveDepth = Math.min(depth, toolRadius) // Limit groove depth to tool radius or specified depth
              toolShape = createCircularGrooveShape(centerX, centerY, grooveRadius, toolRadius, grooveDepth)
              console.log(`🔧 GROOVE: Created circular groove shape at radius ${grooveRadius*1000}mm, depth=${grooveDepth*1000}mm`)

              // Position the groove to start at surface and extend into material
              // For bottom face: start at bottom surface (Z=-doorThickness) and extend upward (positive Z)
              // For top face: start at top surface (Z=0) and extend downward (negative Z)
              const surfaceZ = isBottomFace ?
                (-doorThickness) : // Bottom: start at bottom surface (Z=-doorThickness)
                (0 - grooveDepth)  // Top: start at top surface (Z=0), extend down by groove depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, surfaceZ))
            } else {
              // Circle too small for groove - use drill shape at center
              console.log(`🔧 GROOVE→DRILL: Circle too small for grooving, using drill shape at center`)
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
            }

          } else if (operation === 'pocketing' || operation === 'pocket') {
            // POCKET: Create pocket shape to clear entire circle area
            console.log(`🔧 POCKET: Creating pocket shape inside circle (radius=${circleRadius_mm}mm, tool=${toolRadius*1000}mm)`)

            if (circleRadius > toolRadius * 1.5) {
              // Create shallow pocket shape (surface cut only)
              const pocketDepth = Math.min(depth, circleRadius / 2) // Reasonable pocket depth
              toolShape = createPocketShape(centerX, centerY, circleRadius, toolRadius, pocketDepth)
              console.log(`🔧 POCKET: Created pocket shape with radius ${circleRadius*1000}mm, depth=${pocketDepth*1000}mm`)

              // Position the pocket to start at surface and extend into material
              // For bottom face: start at bottom surface (Z=-doorThickness) and extend upward
              // For top face: start at top surface (Z=0) and extend downward
              const surfaceZ = isBottomFace ?
                (-doorThickness) : // Bottom: start at bottom surface (Z=-doorThickness)
                (0 - pocketDepth)  // Top: start at top surface (Z=0), extend down by pocket depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, surfaceZ))
            } else {
              // Circle too small for pocketing - use drill shape at center
              console.log(`🔧 POCKET→DRILL: Circle too small for pocketing, using drill shape at center`)
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
            }

          } else {
            // Default operation (profiling/finishing) - drill at center
            console.log(`🔧 DEFAULT: Unknown operation '${operation}', drilling at center`)
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
          }

          console.log(`🔧 Circle operation: center=(${centerX_mm}, ${centerY_mm})mm, radius=${circleRadius_mm}mm, tool=${tool.name}(${toolRadius*1000}mm)`)

        } else if (command.command_type === 'polyline') {
          // Handle polyline operations - different operations based on layer name
          const points = (command as any).points || []
          if (points.length >= 2) {
            // Calculate polyline bounding box
            let minX = points[0].x, maxX = points[0].x
            let minY = points[0].y, maxY = points[0].y

            points.forEach((point: any) => {
              minX = Math.min(minX, point.x)
              maxX = Math.max(maxX, point.x)
              minY = Math.min(minY, point.y)
              maxY = Math.max(maxY, point.y)
            })

            const centerX_mm = (minX + maxX) / 2
            const centerY_mm = (minY + maxY) / 2
            const polyWidth_mm = maxX - minX
            const polyHeight_mm = maxY - minY

            // Convert door dimensions from meters to mm for coordinate calculation
            const doorWidth_mm = doorWidth * 1000
            const doorHeight_mm = doorHeight * 1000

            // Account for door offset and center the coordinates, then convert to meters
            const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
            const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

            // Determine operation type from layer name
            const layerName = command.layer_name || tool.name || ''
            const operation = determineOperationFromLayerName(layerName)
            const toolRadius = radius // Tool radius in meters

            console.log(`🔧 Polyline Layer: ${layerName} → Operation: ${operation}`)
            console.log(`🔧 Polyline Points: ${points.length} points`, points)

            if (operation === 'drilling') {
              // POLYLINE DRILLING: Use cylindrical tool at polyline center
              console.log(`🔧 POLYLINE DRILLING: Using cylindrical tool at polyline center`)
              // Keep the original cylindrical toolShape (already created above)
              const drillZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

            } else if (operation === 'grooving' || operation === 'groove') {
              // POLYLINE GROOVE: Use real sweep operation (BRepOffsetAPI_MakePipeShell)
              console.log(`🔧 POLYLINE GROOVE: Creating groove using real sweep operation (BRepOffsetAPI_MakePipeShell)`)
              const startTime = performance.now()

              const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

              try {
                // Convert polyline points to 3D coordinates for sweep operation
                const doorWidth_mm = doorWidth * 1000
                const doorHeight_mm = doorHeight * 1000

                const polylinePoints3D = points.map((point: any) => ({
                  x: ((point.x - doorOffsetX) - doorWidth_mm / 2) / 1000, // Convert to meters and center
                  y: ((point.y - doorOffsetY) - doorHeight_mm / 2) / 1000, // Convert to meters and center
                  z: isBottomFace ? (-doorThickness + grooveDepth/2) : (0 - grooveDepth/2) // Position at appropriate depth
                }))

                console.log(`🔧 POLYLINE GROOVE: Converting ${points.length} points to 3D coordinates for sweep`)

                // Create circular profile for the groove (tool cross-section)
                const grooveRadius = toolRadius * 0.8 // Slightly smaller than tool for realistic groove
                const profileCircle = new oc.BRepPrimAPI_MakeCylinder_1(grooveRadius, grooveDepth)
                const profileShape = profileCircle.Shape()

                // Use the new real sweep function
                const sweptGroove = await createRealSweepFromPolyline(oc, polylinePoints3D, profileShape)

                if (sweptGroove && !sweptGroove.IsNull()) {
                  toolShape = sweptGroove
                  console.log(`✅ POLYLINE GROOVE: Real sweep completed in ${(performance.now() - startTime).toFixed(1)}ms`)

                  // Position transformation is identity since sweep already positioned the shape
                  positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
                } else {
                  throw new Error('Real sweep operation failed')
                }

              } catch (sweepError) {
                console.error(`❌ POLYLINE GROOVE: Real sweep failed, falling back to legacy method:`, sweepError)

                // Fallback to legacy capsule approach
                try {
                  let combinedGroove: any = null

                  // Create individual groove segments as capsules (cylinder + rounded ends)
                  const grooveSegments: any[] = []

                for (let i = 0; i < points.length - 1; i++) {
                  const p1 = points[i]
                  const p2 = points[i + 1]

                  // Convert to door coordinate system
                  const p1X = ((p1.x - doorOffsetX) - doorWidth_mm / 2) / 1000
                  const p1Y = ((p1.y - doorOffsetY) - doorHeight_mm / 2) / 1000
                  const p2X = ((p2.x - doorOffsetX) - doorWidth_mm / 2) / 1000
                  const p2Y = ((p2.y - doorOffsetY) - doorHeight_mm / 2) / 1000

                  // Calculate segment properties
                  const deltaX = p2X - p1X
                  const deltaY = p2Y - p1Y
                  const segmentLength = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

                  if (segmentLength > 0.001) { // Only process segments longer than 1mm
                    // Calculate segment center and orientation
                    const centerX = (p1X + p2X) / 2
                    const centerY = (p1Y + p2Y) / 2
                    const angle = Math.atan2(deltaY, deltaX)

                    // Create a capsule-like shape: cylinder with hemispherical ends
                    // For efficiency, we'll use a rectangular box with rounded corners
                    const boxWidth = segmentLength + (toolRadius * 2) // Add tool radius on each end
                    const boxHeight = toolRadius * 2
                    const boxDepth = grooveDepth

                    // Create the main box
                    const box = new oc.BRepPrimAPI_MakeBox_2(boxWidth, boxHeight, boxDepth)
                    let segmentShape = box.Shape()

                    // Position and orient the box
                    const segmentTf = new oc.gp_Trsf_1()
                    
                    // First translate to center the box at origin
                    segmentTf.SetTranslation_1(new oc.gp_Vec_4(-boxWidth/2, -boxHeight/2, 0))
                    
                    // Then rotate around Z-axis
                    if (Math.abs(angle) > 0.001) {
                      const rotationTf = new oc.gp_Trsf_1()
                      const zDir = new oc.gp_Dir_3(new oc.gp_Vec_4(0, 0, 1))
                      const zAxis = new oc.gp_Ax1_2(new oc.gp_Pnt_3(0, 0, 0), zDir)
                      rotationTf.SetRotation_1(zAxis, angle)
                      segmentTf.Multiply(rotationTf)
                    }
                    
                    // Finally translate to segment center
                    const finalTf = new oc.gp_Trsf_1()
                    const grooveZ = isBottomFace ?
                      (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                      (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
                    finalTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, grooveZ))
                    segmentTf.Multiply(finalTf)

                    const segmentLoc = new oc.TopLoc_Location_2(segmentTf)
                    segmentShape = segmentShape.Moved(segmentLoc, false)
                    
                    grooveSegments.push(segmentShape)
                    console.log(`🔧 POLYLINE GROOVE: Created segment ${i}: length=${segmentLength.toFixed(3)}m, angle=${(angle * 180 / Math.PI).toFixed(1)}°`)
                  }
                }

                // Combine all segments efficiently using a single compound operation
                if (grooveSegments.length > 0) {
                  if (grooveSegments.length === 1) {
                    combinedGroove = grooveSegments[0]
                  } else {
                    // Use compound builder for better performance
                    const compound = new oc.TopoDS_Compound()
                    const builder = new oc.BRep_Builder()
                    builder.MakeCompound(compound)
                    
                    grooveSegments.forEach((segment) => {
                      builder.Add(compound, segment)
                    })
                    
                    combinedGroove = compound
                    console.log(`🔧 POLYLINE GROOVE: Combined ${grooveSegments.length} segments into compound`)
                  }
                }

                  if (combinedGroove) {
                    toolShape = combinedGroove
                    positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
                    const endTime = performance.now()
                    console.log(`🔧 POLYLINE GROOVE: Created efficient groove using ${grooveSegments.length} capsule segments in ${(endTime - startTime).toFixed(2)}ms`)
                  } else {
                    throw new Error(`Failed to create polyline groove segments - no valid segments generated from ${points.length} points`)
                  }
                } catch (error) {
                  console.error(`🔧 POLYLINE GROOVE: Capsule method failed: ${error}`)
                  throw new Error(`Polyline groove creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
                }
              }

            } else {
              // POLYLINE POCKET: Create pocket covering polyline area
              console.log(`🔧 POLYLINE POCKET: Creating pocket covering polyline area`)

              const polyWidth = polyWidth_mm / 1000  // Convert to meters
              const polyHeight = polyHeight_mm / 1000 // Convert to meters
              const pocketDepth = Math.min(depth, Math.min(polyWidth, polyHeight) / 2) // Reasonable pocket depth

              // Create bounding box pocket
              const pocketBox = new oc.BRepPrimAPI_MakeBox_2(polyWidth, polyHeight, pocketDepth)
              toolShape = pocketBox.Shape()

              // Position the pocket
              const pocketZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX - polyWidth/2, centerY - polyHeight/2, pocketZ))
            }

            console.log(`🔧 Polyline operation: center=(${centerX_mm}, ${centerY_mm})mm, bounds=${polyWidth_mm.toFixed(1)}×${polyHeight_mm.toFixed(1)}mm, operation=${operation}`)
          }

        } else if (command.command_type === 'rectangle') {
          // Handle rectangle operations - different operations based on layer name
          const x1_mm = command.x1 || 0
          const y1_mm = command.y1 || 0
          const x2_mm = command.x2 || 0
          const y2_mm = command.y2 || 0

          // Calculate rectangle center and dimensions
          const centerX_mm = (x1_mm + x2_mm) / 2
          const centerY_mm = (y1_mm + y2_mm) / 2
          const rectWidth_mm = Math.abs(x2_mm - x1_mm)
          const rectHeight_mm = Math.abs(y2_mm - y1_mm)

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Determine operation type from layer name (same logic as circles)
          const layerName = command.layer_name || tool.name || ''
          const operation = determineOperationFromLayerName(layerName)
          const toolRadius = radius // Tool radius in meters

          console.log(`🔧 Rectangle Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling') {
            // RECTANGLE DRILLING: Use cylindrical tool at rectangle center (like a hole in center)
            console.log(`🔧 RECT DRILLING: Using cylindrical tool at rectangle center`)
            // Keep the original cylindrical toolShape (already created above)
            // Position at rectangle center
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // RECTANGLE GROOVE: Create rectangular outline/border (hollow rectangle)
            console.log(`🔧 RECT GROOVE: Creating rectangular groove outline`)

            const rectWidth = rectWidth_mm / 1000  // Convert to meters
            const rectHeight = rectHeight_mm / 1000 // Convert to meters
            const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

            // Create hollow rectangular groove (outer box - inner box)
            const outerWidth = rectWidth + toolRadius
            const outerHeight = rectHeight + toolRadius
            const innerWidth = Math.max(rectWidth - toolRadius, toolRadius/2)
            const innerHeight = Math.max(rectHeight - toolRadius, toolRadius/2)

            // Create outer box
            const outerBox = new oc.BRepPrimAPI_MakeBox_2(outerWidth, outerHeight, grooveDepth)
            // Create inner box to subtract
            const innerBox = new oc.BRepPrimAPI_MakeBox_2(innerWidth, innerHeight, grooveDepth)

            // Position inner box at center of outer box
            const innerTf = new oc.gp_Trsf_1()
            innerTf.SetTranslation_1(new oc.gp_Vec_4((outerWidth - innerWidth)/2, (outerHeight - innerHeight)/2, 0))
            const innerLoc = new oc.TopLoc_Location_2(innerTf)
            const movedInnerBox = innerBox.Shape().Moved(innerLoc, false)

            // Subtract inner from outer to create groove
            const cut = new oc.BRepAlgoAPI_Cut_3(
              outerBox.Shape(),
              movedInnerBox,
              new oc.Message_ProgressRange_1()
            )
            cut.Build(new oc.Message_ProgressRange_1())
            toolShape = cut.Shape()

            // Position the groove
            const grooveZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX - outerWidth/2, centerY - outerHeight/2, grooveZ))

          } else {
            // RECTANGLE POCKET: Create full rectangular pocket (solid box removal)
            console.log(`🔧 RECT POCKET: Creating full rectangular pocket`)

            const rectWidth = rectWidth_mm / 1000  // Convert to meters
            const rectHeight = rectHeight_mm / 1000 // Convert to meters
            const pocketDepth = Math.min(depth, Math.min(rectWidth, rectHeight) / 2) // Reasonable pocket depth

            // Create a solid box shape for the rectangle pocket
            const rectBox = new oc.BRepPrimAPI_MakeBox_2(rectWidth, rectHeight, pocketDepth)
            toolShape = rectBox.Shape()

            // Position the rectangular pocket
            const pocketZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX - rectWidth/2, centerY - rectHeight/2, pocketZ))
          }

          console.log(`🔧 Rectangle operation: center=(${centerX_mm}, ${centerY_mm})mm, size=${rectWidth_mm}×${rectHeight_mm}mm, operation=${operation}`)

        } else if (command.command_type === 'arc') {
          // Handle arc operations - different operations based on layer name
          const centerX_mm = command.x1 || 0
          const centerY_mm = command.y1 || 0
          const arcRadius_mm = command.radius || 0
          const startAngle = (command as any).start_angle || 0
          const endAngle = (command as any).end_angle || 360

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Determine operation type from layer name
          const layerName = command.layer_name || tool.name || ''
          const operation = determineOperationFromLayerName(layerName)
          const toolRadius = radius // Tool radius in meters
          const arcRadius = arcRadius_mm / 1000 // Convert to meters

          console.log(`🔧 Arc Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling') {
            // ARC DRILLING: Use cylindrical tool at arc center
            console.log(`🔧 ARC DRILLING: Using cylindrical tool at arc center`)
            // Keep the original cylindrical toolShape (already created above)
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // ARC GROOVE: Create arc-shaped groove (torus section or arc outline)
            console.log(`🔧 ARC GROOVE: Creating arc-shaped groove`)

            const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

            if (arcRadius > toolRadius) {
              // Create arc groove using torus section (simplified to annular cylinder for now)
              const outerRadius = arcRadius + toolRadius / 2
              const innerRadius = Math.max(arcRadius - toolRadius / 2, toolRadius / 4)

              // Create outer cylinder
              const outerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(outerRadius, grooveDepth)
              // Create inner cylinder to subtract
              const innerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(innerRadius, grooveDepth)

              // Subtract inner from outer to create annular groove
              const cut = new oc.BRepAlgoAPI_Cut_3(
                outerCylinder.Shape(),
                innerCylinder.Shape(),
                new oc.Message_ProgressRange_1()
              )
              cut.Build(new oc.Message_ProgressRange_1())
              toolShape = cut.Shape()

              // Position the groove
              const grooveZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, grooveZ))
            } else {
              // Arc too small for groove - use drill shape at center
              const drillZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))
            }

          } else {
            // ARC POCKET: Create circular pocket at arc center
            console.log(`🔧 ARC POCKET: Creating circular pocket at arc center`)

            const pocketRadius = Math.max(arcRadius, toolRadius * 2) // Ensure reasonable pocket size
            const pocketDepth = Math.min(depth, arcRadius / 2) // Reasonable pocket depth

            // Create circular pocket
            const pocketCylinder = new oc.BRepPrimAPI_MakeCylinder_1(pocketRadius, pocketDepth)
            toolShape = pocketCylinder.Shape()

            // Position the pocket
            const pocketZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, pocketZ))
          }

          console.log(`🔧 Arc operation: center=(${centerX_mm}, ${centerY_mm})mm, radius=${arcRadius_mm}mm, angles=${startAngle}°-${endAngle}°, operation=${operation}`)

        } else {
          console.warn(`🔧 Unknown command type: ${command.command_type}, skipping`)
          return // Skip this command
        }

        const positionLoc = new oc.TopLoc_Location_2(positionTf)
        const positionedTool = toolShape.Moved(positionLoc, false)
        positionedShapes.push(positionedTool)

        // Cache the positioned tool with unique ID
        const toolId = `positioned_tool_${tool.name}_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        toolCache.set(toolId, positionedTool)
        shapeIds.push(toolId)
      }
    }

    console.log(`✅ Created ${positionedShapes.length} positioned tool shapes`)

    return {
      success: true,
      count: positionedShapes.length,
      shapeIds: shapeIds
    }
  } catch (error) {
    console.error('Error creating positioned tool shapes:', error)
    throw error
  }
}

// Create individual tool BRep for sweep operations
function createToolBRep(params: ToolBRepParams): any {
  const { tool, height = 50, includeGLB = false } = params

  try {
    let toolShape: any = null
    const radius = tool.diameter / 2

    console.log(`Creating BRep for ${tool.shape} tool: ${tool.name} (⌀${tool.diameter}mm)`)

    switch (tool.shape) {
      case 'cylindrical':
        // Create cylindrical tool BRep
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = cylinder.Shape()
        break

      case 'conical':
        // Create conical tool (V-bit) BRep
        const conicalTool = tool as any // ConicalTool
        const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
        const cone = new oc.BRepPrimAPI_MakeCone_1(tipRadius, radius, height)
        toolShape = cone.Shape()
        break

      case 'ballnose':
        // Create ballnose tool BRep (hemisphere + cylinder)
        const ballnoseTool = tool as any // BallnoseTool
        const ballRadius = ballnoseTool.ballRadius || radius

        // Create cylinder part
        const cylHeight = Math.max(height - ballRadius, 0)
        const ballCylinder = new oc.BRepPrimAPI_MakeCylinder_1(ballRadius, cylHeight)

        // Create hemisphere part
        const hemisphere = new oc.BRepPrimAPI_MakeSphere_1(ballRadius)

        // Position hemisphere at bottom
        const tf = new oc.gp_Trsf_1()
        tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -ballRadius))
        const loc = new oc.TopLoc_Location_2(tf)
        const movedHemisphere = hemisphere.Shape().Moved(loc, false)

        // Fuse cylinder and hemisphere
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          ballCylinder.Shape(),
          movedHemisphere,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        if (fuse.IsDone()) {
          toolShape = fuse.Shape()
        } else {
          toolShape = ballCylinder.Shape() // Fallback to cylinder
        }
        break

      case 'radial':
        // Create radial tool BRep (cylinder with rounded corners)
        // radialTool and cornerRadius calculation removed as they're not used in current implementation

        // For now, create a simple cylinder (could be enhanced with actual corner radius)
        const radialCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = radialCylinder.Shape()
        break

      case 'special':
        // Create special tool BRep (default to cylinder for now)
        const specialCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = specialCylinder.Shape()
        break

      default:
        // Default to cylindrical
        const defaultCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = defaultCylinder.Shape()
    }

    if (!toolShape) {
      throw new Error(`Failed to create BRep for tool: ${tool.name}`)
    }

    // Cache the tool shape
    const toolId = tool.id || tool.name || 'unknown'
    const shapeId = `tool_${toolId}_${Date.now()}`
    shapeCache.set(shapeId, toolShape)
    console.log(`✅ Tool BRep cached with ID: ${shapeId}`)

    // Generate GLB if requested
    let glbData: ArrayBuffer | null = null
    if (includeGLB) {
      glbData = exportToGLB(toolShape)
    }

    return {
      success: true,
      toolId: tool.id,
      toolName: tool.name,
      toolShape: tool.shape,
      diameter: tool.diameter,
      height: height,
      shapeId: shapeId,
      glbData: glbData,
      // Actual BRep shape is stored in toolCache for later use
    }
  } catch (error) {
    console.error(`Error creating BRep for tool ${tool.name}:`, error)
    throw error
  }
}

// Create BReps for all tools
function createAllToolBReps(params: AllToolBRepsParams): any {
  const { tools, height = 50, includeGLB = false } = params

  try {
    const results: any[] = []

    console.log(`Creating BReps for ${tools.length} tools...`)

    tools.forEach(tool => {
      try {
        const toolResult = createToolBRep({ tool, height, includeGLB })
        results.push(toolResult)
        console.log(`✓ Created BRep for ${tool.name}`)
      } catch (error) {
        console.error(`✗ Failed to create BRep for ${tool.name}:`, error)
        results.push({
          success: false,
          toolId: tool.id,
          toolName: tool.name,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    })

    return {
      success: true,
      count: results.length,
      successCount: results.filter(r => r.success).length,
      results: results
    }
  } catch (error) {
    console.error('Error creating tool BReps:', error)
    throw error
  }
}

// Perform sweep operation (boolean subtraction)
function performSweepOperation(params: SweepOperationParams): any {
  const { doorBodyShape, toolGeometries, operation } = params

  try {
    console.log(`🔧 Starting sweep operation: ${operation}`)

    // Get the door body shape from cache
    let resultShape: any = null
    if (typeof doorBodyShape === 'string') {
      resultShape = shapeCache.get(doorBodyShape)
      if (!resultShape) {
        throw new Error(`Door body shape not found in cache: ${doorBodyShape}`)
      }
    } else if (doorBodyShape && doorBodyShape.shapeId) {
      resultShape = shapeCache.get(doorBodyShape.shapeId)
      if (!resultShape) {
        throw new Error(`Door body shape not found in cache: ${doorBodyShape.shapeId}`)
      }
    } else {
      // Assume doorBodyShape is the actual shape
      resultShape = doorBodyShape
    }

    console.log(`🔧 Processing ${toolGeometries.length} tool geometries`)

    // Debug: Log initial processing
    console.log(`🔍 Starting CSG operations on door body`)

    let successfulOperations = 0

    toolGeometries.forEach((toolGeometry, index) => {
      try {
        let toolShape: any = null

        // Get tool shape from cache - check both toolCache and shapeCache
        if (typeof toolGeometry === 'string') {
          toolShape = toolCache.get(toolGeometry) || shapeCache.get(toolGeometry)
          console.log(`🔍 Looking for tool shape ID: ${toolGeometry}`)
          console.log(`🔍 Found in toolCache: ${!!toolCache.get(toolGeometry)}`)
          console.log(`🔍 Found in shapeCache: ${!!shapeCache.get(toolGeometry)}`)
        } else if (toolGeometry && toolGeometry.shapeId) {
          toolShape = toolCache.get(toolGeometry.shapeId) || shapeCache.get(toolGeometry.shapeId)
          console.log(`🔍 Looking for tool shape ID: ${toolGeometry.shapeId}`)
        } else {
          // Assume toolGeometry is the actual shape
          toolShape = toolGeometry
          console.log(`🔍 Using direct tool shape object`)
        }

        if (!toolShape) {
          console.warn(`⚠️ Tool shape ${index} not found in any cache, skipping`)
          console.log(`🔍 Available toolCache keys: ${Array.from(toolCache.keys()).join(', ')}`)
          console.log(`🔍 Available shapeCache keys: ${Array.from(shapeCache.keys()).join(', ')}`)
          return
        }

        console.log(`✅ Found tool shape ${index} for operation`)

        if (operation === 'subtract') {
          console.log(`🔧 Attempting to subtract tool ${index}...`)
          const cut = new oc.BRepAlgoAPI_Cut_3(
            resultShape,
            toolShape,
            new oc.Message_ProgressRange_1()
          )
          cut.Build(new oc.Message_ProgressRange_1())
          if (cut.IsDone()) {
            const newShape = cut.Shape()

            console.log(`✅ Tool ${index} subtracted successfully`)

            // Simple validation that we have a valid shape after subtraction
            if (newShape && !newShape.IsNull()) {
              console.log(`📊 Boolean operation completed - result shape is valid`)
            } else {
              console.warn(`⚠️ Boolean operation may have failed - invalid result shape`)
            }

            resultShape = newShape
            successfulOperations++
          } else {
            console.warn(`⚠️ Tool ${index} subtraction failed - boolean operation did not complete`)
          }
        } else if (operation === 'union') {
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            resultShape,
            toolShape,
            new oc.Message_ProgressRange_1()
          )
          fuse.Build(new oc.Message_ProgressRange_1())
          if (fuse.IsDone()) {
            resultShape = fuse.Shape()
            console.log(`✅ Tool ${index} union completed successfully`)
          } else {
            console.warn(`⚠️ Tool ${index} union failed`)
          }
        }
      } catch (toolError) {
        console.error(`❌ Error processing tool ${index}:`, toolError)
      }
    })

    // Cache the result shape
    const resultShapeId = `result_${Date.now()}`
    shapeCache.set(resultShapeId, resultShape)
    console.log(`✅ Sweep operation completed, result cached with ID: ${resultShapeId}`)
    console.log(`📊 Successfully processed ${successfulOperations} out of ${toolGeometries.length} tools`)

    if (successfulOperations === 0) {
      console.warn(`⚠️ No tools were successfully processed - result may be unchanged`)
    }

    return {
      success: true,
      shapeId: resultShapeId,
      operation: operation,
      toolsProcessed: successfulOperations
    }
  } catch (error) {
    console.error('❌ Error performing sweep operation:', error)
    throw error
  }
}

// Export shape to GLB format
function exportToGLB(shapeOrId: any): ArrayBuffer {
  try {
    console.log('🔧 Exporting to GLB...')

    // Get the actual shape
    let shape: any = null
    if (typeof shapeOrId === 'string') {
      // It's a shape ID, get from cache
      shape = shapeCache.get(shapeOrId)
      if (!shape) {
        throw new Error(`Shape not found in cache: ${shapeOrId}`)
      }
    } else if (shapeOrId && shapeOrId.shapeId) {
      // It's an object with shapeId
      shape = shapeCache.get(shapeOrId.shapeId)
      if (!shape) {
        throw new Error(`Shape not found in cache: ${shapeOrId.shapeId}`)
      }
    } else {
      // Assume it's the actual shape
      shape = shapeOrId
    }

    if (!shape) {
      throw new Error('No valid shape provided for GLB export')
    }

    console.log(`🔍 Exporting final shape to GLB format`)

    // Create a document and add our shape
    const docHandle = new oc.Handle_TDocStd_Document_2(
      new oc.TDocStd_Document(new oc.TCollection_ExtendedString_1())
    )
    const shapeTool = oc.XCAFDoc_DocumentTool.ShapeTool(docHandle.get().Main()).get()
    shapeTool.SetShape(shapeTool.NewShape(), shape)

    // Mesh the shape with higher resolution for better wireframe visibility
    // Using smaller deflection values for finer mesh detail
    new oc.BRepMesh_IncrementalMesh_2(shape, 0.01, false, 0.01, false)

    // Export GLB file
    const cafWriter = new oc.RWGltf_CafWriter(
      new oc.TCollection_AsciiString_2('./result.glb'),
      true
    )
    cafWriter.Perform_2(
      docHandle,
      new oc.TColStd_IndexedDataMapOfStringString_1(),
      new oc.Message_ProgressRange_1()
    )

    // Read the GLB file from virtual file system
    const glbFile = oc.FS.readFile('./result.glb', { encoding: 'binary' })
    console.log('✅ GLB export completed, size:', glbFile.buffer.byteLength, 'bytes')
    return glbFile.buffer
  } catch (error) {
    console.error('❌ Error exporting to GLB:', error)
    throw error
  }
}

// Worker message handler
self.onmessage = async (event: MessageEvent<OCJSWorkerMessage>) => {
  const { id, type, data } = event.data

  try {
    console.log(`🔧 Worker received message: ${type}`)
    
    // Initialize OpenCascade.js with timeout
    console.log('🔧 Ensuring OpenCascade.js is initialized...')
    await initializeOC()
    console.log('✅ OpenCascade.js ready, proceeding with operation...')

    let result: any = null

    switch (type) {
      case 'ping':
        console.log('🏓 Worker ping received')
        result = { success: true, message: 'pong', timestamp: Date.now() }
        break
      case 'createDoorBody':
        console.log('🚪 Creating door body...')
        result = createDoorBody(data as DoorBodyParams)
        break
      case 'createToolGeometry':
        console.log('🔧 Creating tool geometry...')
        result = createToolGeometry(data as ToolGeometryParams)
        break
      case 'performSweepOperation':
        console.log('🔄 Performing sweep operation...')
        result = performSweepOperation(data as SweepOperationParams)
        break
      case 'exportGLB':
        console.log('📦 Exporting to GLB...')
        result = exportToGLB(data)
        break
      case 'createToolBRep':
        console.log('🔧 Creating tool BRep...')
        result = createToolBRep(data as ToolBRepParams)
        break
      case 'createAllToolBReps':
        console.log('🔧 Creating all tool BReps...')
        result = createAllToolBReps(data as AllToolBRepsParams)
        break
      case 'createPositionedToolShapes':
        console.log('🔧 Creating positioned tool shapes...')
        result = await createPositionedToolShapes(data)
        break
      case 'createSimpleBoxGLB':
        console.log('📦 Creating simple box GLB...')
        result = createSimpleBoxGLB(data as DoorBodyParams)
        break
      case 'testPolylineSweep':
        console.log('🧪 Testing polyline sweep...')
        result = await testPolylineSweep(data as TestPolylineSweepParams)
        break
      default:
        throw new Error(`Unknown operation type: ${type}`)
    }

    const response: OCJSWorkerResponse = {
      id,
      type: 'success',
      data: result
    }

    console.log(`✅ Worker completed: ${type}`)
    self.postMessage(response)
  } catch (error) {
    console.error(`❌ Worker error for ${type}:`, error)
    
    // Provide more detailed error information
    let errorMessage = 'Unknown worker error'
    if (error instanceof Error) {
      errorMessage = error.message
      console.error('❌ Error stack:', error.stack)
    }
    
    const response: OCJSWorkerResponse = {
      id,
      type: 'error',
      error: errorMessage
    }

    self.postMessage(response)
  }
}

// Handle worker errors
self.onerror = (error) => {
  console.error('Worker global error:', error)
}

self.onunhandledrejection = (event) => {
  console.error('Worker unhandled rejection:', event.reason)
}

/**
 * Create swept geometry using BRepOffsetAPI_MakePipeShell (dddd.md compliant)
 * @param oc OpenCascade.js module instance
 * @param polylinePoints Array of 3D points defining the sweep path
 * @param profileShape TopoDS_Shape representing the profile to sweep
 * @returns Promise<any> The resulting swept solid shape
 */
async function createRealSweepFromPolyline(
  oc: any,
  polylinePoints: { x: number; y: number; z: number }[],
  profileShape: any
): Promise<any> {
  try {
    // Validate inputs
    if (!oc) {
      throw new Error('OpenCascade.js module not initialized')
    }

    if (!polylinePoints || polylinePoints.length < 2) {
      throw new Error('At least 2 points required for polyline sweep')
    }

    if (!profileShape) {
      throw new Error('Profile shape is required for sweep operation')
    }

    console.log(`🔄 Creating REAL sweep along polyline with ${polylinePoints.length} points using BRepOffsetAPI_MakePipeShell`)

    // Step 1: Create tool path (spine) as TopoDS_Wire
    const toolPath = await createToolPathWire(oc, polylinePoints)

    // Step 2: Create swept volume using BRepOffsetAPI_MakePipeShell
    console.log('🔧 Creating BRepOffsetAPI_MakePipeShell...')
    const pipeMaker = new oc.BRepOffsetAPI_MakePipeShell(toolPath)

    // Step 3: Add profile shape to be swept along the path
    console.log('🔧 Adding profile shape to pipe maker...')
    pipeMaker.Add(profileShape, false, false)

    // Step 4: Build the sweep operation
    console.log('🔧 Building sweep operation...')
    pipeMaker.Build(new oc.Message_ProgressRange_1())

    if (!pipeMaker.IsDone()) {
      throw new Error('BRepOffsetAPI_MakePipeShell build failed')
    }

    // Step 5: Make solid from the swept shell
    console.log('🔧 Making solid from swept shell...')
    pipeMaker.MakeSolid()

    const sweptVolume = pipeMaker.Shape()

    if (!sweptVolume || sweptVolume.IsNull()) {
      throw new Error('Swept volume is null or invalid')
    }

    console.log('✅ Real sweep operation completed successfully using BRepOffsetAPI_MakePipeShell')

    // Clean up
    toolPath.delete()
    pipeMaker.delete()

    return sweptVolume

  } catch (error) {
    console.error('❌ Error in createRealSweepFromPolyline:', error)
    // Fallback to old method
    console.log('🔄 Falling back to old sweep method...')
    return createSweepFromPolyline(oc, polylinePoints, profileShape)
  }
}

/**
 * Create TopoDS_Wire from polyline points for use as tool path
 * @param oc OpenCascade.js module instance
 * @param polylinePoints Array of 3D points
 * @returns Promise<any> TopoDS_Wire representing the tool path
 */
async function createToolPathWire(
  oc: any,
  polylinePoints: { x: number; y: number; z: number }[]
): Promise<any> {
  console.log(`🔧 Creating tool path wire from ${polylinePoints.length} points`)

  // Create edges between consecutive points
  const edges: any[] = []

  for (let i = 0; i < polylinePoints.length - 1; i++) {
    const p1 = polylinePoints[i]
    const p2 = polylinePoints[i + 1]

    // Create OpenCascade points
    const ocP1 = new oc.gp_Pnt_3(p1.x, p1.y, p1.z)
    const ocP2 = new oc.gp_Pnt_3(p2.x, p2.y, p2.z)

    // Create line segment between points
    const lineSegment = new oc.GC_MakeSegment_1(ocP1, ocP2)
    const curve = new oc.Handle_Geom_Curve_2(lineSegment.Value().get())

    // Create edge from curve
    const edgeBuilder = new oc.BRepBuilderAPI_MakeEdge_24(curve)

    if (!edgeBuilder.IsDone()) {
      throw new Error(`Failed to create edge ${i} for tool path`)
    }

    const edge = edgeBuilder.Edge()
    edges.push(edge)

    console.log(`🔗 Created tool path edge ${i}: (${p1.x.toFixed(3)}, ${p1.y.toFixed(3)}, ${p1.z.toFixed(3)}) → (${p2.x.toFixed(3)}, ${p2.y.toFixed(3)}, ${p2.z.toFixed(3)})`)
  }

  // Create wire from edges
  const wireBuilder = new oc.BRepBuilderAPI_MakeWire_2()

  for (const edge of edges) {
    wireBuilder.Add_2(edge)
  }

  if (!wireBuilder.IsDone()) {
    throw new Error('Failed to create tool path wire')
  }

  const toolPath = wireBuilder.Wire()

  if (!toolPath || toolPath.IsNull()) {
    throw new Error('Tool path wire is null')
  }

  console.log('✅ Tool path wire created successfully')

  return toolPath
}

/**
 * Create TopoDS_Wire from DrawCommand array for use as tool path
 * @param oc OpenCascade.js module instance
 * @param commands Array of DrawCommand objects
 * @param doorWidth Door width in meters
 * @param doorHeight Door height in meters
 * @param doorOffsetX Door offset X in mm
 * @param doorOffsetY Door offset Y in mm
 * @returns Promise<any> TopoDS_Wire representing the tool path
 */
async function createToolPathFromCommands(
  oc: any,
  commands: DrawCommand[],
  doorWidth: number,
  doorHeight: number,
  doorOffsetX: number = 0,
  doorOffsetY: number = 0
): Promise<any> {
  console.log(`🔧 Creating tool path wire from ${commands.length} DrawCommands`)

  const edges: any[] = []
  const doorWidth_mm = doorWidth * 1000
  const doorHeight_mm = doorHeight * 1000

  for (const command of commands) {
    if (command.command_type === 'line') {
      // Convert line command to 3D points
      const x1 = ((command.x1 - doorOffsetX) - doorWidth_mm / 2) / 1000
      const y1 = ((command.y1 - doorOffsetY) - doorHeight_mm / 2) / 1000
      const x2 = ((command.x2 - doorOffsetX) - doorWidth_mm / 2) / 1000
      const y2 = ((command.y2 - doorOffsetY) - doorHeight_mm / 2) / 1000
      const z = 0 // Tool path at surface level

      // Create OpenCascade points
      const ocP1 = new oc.gp_Pnt_3(x1, y1, z)
      const ocP2 = new oc.gp_Pnt_3(x2, y2, z)

      // Create line segment between points
      const lineSegment = new oc.GC_MakeSegment_1(ocP1, ocP2)
      const curve = new oc.Handle_Geom_Curve_2(lineSegment.Value().get())

      // Create edge from curve
      const edgeBuilder = new oc.BRepBuilderAPI_MakeEdge_24(curve)

      if (!edgeBuilder.IsDone()) {
        throw new Error(`Failed to create edge for line command`)
      }

      const edge = edgeBuilder.Edge()
      edges.push(edge)

      console.log(`🔗 Created tool path edge from line: (${x1.toFixed(3)}, ${y1.toFixed(3)}) → (${x2.toFixed(3)}, ${y2.toFixed(3)})`)

    } else if (command.command_type === 'polyline') {
      // Convert polyline command to connected edges
      const points = (command as any).points || []

      for (let i = 0; i < points.length - 1; i++) {
        const p1 = points[i]
        const p2 = points[i + 1]

        // Convert to door coordinate system
        const x1 = ((p1.x - doorOffsetX) - doorWidth_mm / 2) / 1000
        const y1 = ((p1.y - doorOffsetY) - doorHeight_mm / 2) / 1000
        const x2 = ((p2.x - doorOffsetX) - doorWidth_mm / 2) / 1000
        const y2 = ((p2.y - doorOffsetY) - doorHeight_mm / 2) / 1000
        const z = 0 // Tool path at surface level

        // Create OpenCascade points
        const ocP1 = new oc.gp_Pnt_3(x1, y1, z)
        const ocP2 = new oc.gp_Pnt_3(x2, y2, z)

        // Create line segment between points
        const lineSegment = new oc.GC_MakeSegment_1(ocP1, ocP2)
        const curve = new oc.Handle_Geom_Curve_2(lineSegment.Value().get())

        // Create edge from curve
        const edgeBuilder = new oc.BRepBuilderAPI_MakeEdge_24(curve)

        if (!edgeBuilder.IsDone()) {
          throw new Error(`Failed to create edge for polyline segment ${i}`)
        }

        const edge = edgeBuilder.Edge()
        edges.push(edge)

        console.log(`🔗 Created tool path edge from polyline segment ${i}: (${x1.toFixed(3)}, ${y1.toFixed(3)}) → (${x2.toFixed(3)}, ${y2.toFixed(3)})`)
      }

    } else if (command.command_type === 'arc') {
      // Convert arc command to arc edge
      const centerX = ((command.x1 - doorOffsetX) - doorWidth_mm / 2) / 1000
      const centerY = ((command.y1 - doorOffsetY) - doorHeight_mm / 2) / 1000
      const radius = (command.radius || 0) / 1000
      const startAngle = command.start_angle || 0
      const endAngle = command.end_angle || Math.PI * 2
      const z = 0

      // Create arc geometry
      const center = new oc.gp_Pnt_3(centerX, centerY, z)
      const normal = new oc.gp_Dir_4(0, 0, 1) // Z-axis
      const xAxis = new oc.gp_Dir_4(1, 0, 0) // X-axis
      const axis = new oc.gp_Ax2_3(center, normal, xAxis)

      const circle = new oc.gp_Circ_2(axis, radius)
      const arcGeom = new oc.GC_MakeArcOfCircle_4(circle, startAngle, endAngle, true)
      const curve = new oc.Handle_Geom_Curve_2(arcGeom.Value().get())

      // Create edge from arc curve
      const edgeBuilder = new oc.BRepBuilderAPI_MakeEdge_24(curve)

      if (!edgeBuilder.IsDone()) {
        throw new Error(`Failed to create edge for arc command`)
      }

      const edge = edgeBuilder.Edge()
      edges.push(edge)

      console.log(`🔗 Created tool path edge from arc: center=(${centerX.toFixed(3)}, ${centerY.toFixed(3)}), radius=${radius.toFixed(3)}, angles=${startAngle.toFixed(2)}-${endAngle.toFixed(2)}`)
    }
  }

  if (edges.length === 0) {
    throw new Error('No valid edges created from DrawCommands')
  }

  // Create wire from edges
  const wireBuilder = new oc.BRepBuilderAPI_MakeWire_2()

  for (const edge of edges) {
    wireBuilder.Add_2(edge)
  }

  if (!wireBuilder.IsDone()) {
    throw new Error('Failed to create tool path wire from DrawCommands')
  }

  const toolPath = wireBuilder.Wire()

  if (!toolPath || toolPath.IsNull()) {
    throw new Error('Tool path wire is null')
  }

  console.log(`✅ Tool path wire created successfully from ${commands.length} DrawCommands (${edges.length} edges)`)

  return toolPath
}

/**
 * Creates a sweep operation (pipe sweep) along a polyline path (LEGACY METHOD)
 * @param oc OpenCascade.js module instance
 * @param polylinePoints Array of 3D points defining the sweep path
 * @param profileShape TopoDS_Shape representing the profile to sweep
 * @returns Promise<any> The resulting swept solid shape
 */
async function createSweepFromPolyline(
  oc: any,
  polylinePoints: { x: number; y: number; z: number }[],
  profileShape: any
): Promise<any> {
  try {
    // Validate inputs
    if (!oc) {
      throw new Error('OpenCascade.js module not initialized')
    }
    
    if (!polylinePoints || polylinePoints.length < 2) {
      throw new Error('At least 2 points required for polyline sweep')
    }
    
    if (!profileShape) {
      throw new Error('Profile shape is required for sweep operation')
    }

    console.log(`🔄 Creating sweep along polyline with ${polylinePoints.length} points`)

    // Step 1: Convert polyline points to OpenCascade vertices
    const ocVertices: any[] = []
    for (let i = 0; i < polylinePoints.length; i++) {
      const point = polylinePoints[i]
      const ocPoint = new oc.gp_Pnt_3(point.x, point.y, point.z)
      const vertexBuilder = new oc.BRepBuilderAPI_MakeVertex(ocPoint)
      const vertex = vertexBuilder.Vertex()
      ocVertices.push(vertex)
      console.log(`📍 Point ${i}: (${point.x}, ${point.y}, ${point.z})`)
    }

    // Step 2: Create edges between consecutive vertices
    const edges: any[] = []
    for (let i = 0; i < ocVertices.length - 1; i++) {
      try {
        // Create edge directly from two vertices using BRepBuilderAPI_MakeEdge
        const edgeBuilder = new oc.BRepBuilderAPI_MakeEdge_2(ocVertices[i], ocVertices[i + 1])
        
        if (!edgeBuilder.IsDone()) {
          throw new Error(`Failed to create edge between vertices ${i} and ${i + 1}`)
        }
        
        const edge = edgeBuilder.Edge()
        if (edge.IsNull()) {
          throw new Error(`Edge ${i} is null`)
        }
        
        edges.push(edge)
        console.log(`🔗 Created edge ${i} between vertices ${i} and ${i + 1}`)
      } catch (error) {
        const debugInfo = {
          edgeIndex: i,
          point1: polylinePoints[i],
          point2: polylinePoints[i + 1],
          error: error instanceof Error ? error.message : 'Unknown error'
        }
        throw new Error(`Failed to create edge ${i}: ${JSON.stringify(debugInfo)}`)
      }
    }

    // Step 3: Create wire from edges
    const wireBuilder = new oc.BRepBuilderAPI_MakeWire_1()
    
    for (let i = 0; i < edges.length; i++) {
      try {
        wireBuilder.Add_1(edges[i])
        console.log(`🔗 Added edge ${i} to wire`)
      } catch (error) {
        throw new Error(`Failed to add edge ${i} to wire: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Step 4: Validate the wire
    if (!wireBuilder.IsDone()) {
      const debugInfo = {
        pointsCount: polylinePoints.length,
        edgesCount: edges.length,
        points: polylinePoints
      }
      throw new Error(`Wire construction failed - debug info: ${JSON.stringify(debugInfo)}`)
    }

    const wire = wireBuilder.Wire()
    if (wire.IsNull()) {
      const debugInfo = {
        pointsCount: polylinePoints.length,
        edgesCount: edges.length,
        points: polylinePoints
      }
      throw new Error(`Wire is null after construction - debug info: ${JSON.stringify(debugInfo)}`)
    }

    console.log(`✅ Successfully created wire from ${edges.length} edges`)

    // Step 4.5: Validate wire for sweep operation
    try {
      // Try to get some basic wire properties
      console.log(`🔍 Wire is closed: ${wire.Closed()}`)
      console.log(`🔍 Wire is valid for sweep operation`)
    } catch (error) {
      console.log(`⚠️ Wire validation error (continuing anyway):`, error)
    }

    // Step 5: Create swept geometry by placing profile along polyline path
    let sweptShape: any
    try {
      console.log(`🔄 Creating swept geometry by placing profile along polyline...`)
      console.log(`🔄 Wire is valid: ${!wire.IsNull()}`)
      console.log(`🔄 Profile is valid: ${!profileShape.IsNull()}`)
      console.log(`🔄 Profile shape type: ${profileShape.ShapeType()}`)
      
      // Method: Place the actual profile shape at each point along the polyline
      // and create a union of all positioned profiles to form a swept shape
      
      const profileShapes: any[] = []
      
      console.log(`🔄 Placing profile at ${polylinePoints.length} points along polyline...`)
      
      // Get door body thickness for proper penetration
      // Door body is now positioned from Z=0 down to Z=-doorThickness (e.g., 0 to -0.018 for 18mm thickness)
      const doorThickness = 0.025 // 25mm - slightly more than typical door thickness for complete cut
      
      for (let i = 0; i < polylinePoints.length - 1; i++) { // -1 because last point is same as first
        const point = polylinePoints[i]
        
        console.log(`🔗 Placing profile at point ${i}: (${point.x.toFixed(3)}, ${point.y.toFixed(3)}, ${point.z.toFixed(3)})`)
        
        // Create a copy of the profile shape and extend it through the door thickness
        const tf = new oc.gp_Trsf_1()
        
        // Position the profile to penetrate through the entire door body
        // Start from slightly above the door top surface and extend down through the entire thickness
        // Door body ranges from Z=0 down to Z=-doorThickness (e.g., 0 to -0.018 for 18mm thickness)
        const penetrationStartZ = 0.001 // Start 1mm above door top surface
        tf.SetTranslation_1(new oc.gp_Vec_4(point.x, point.y, penetrationStartZ))
        
        const loc = new oc.TopLoc_Location_2(tf)
        const positionedProfile = profileShape.Moved(loc, false)
        
        // Extrude the profile down through the door thickness to ensure complete penetration
        try {
          const extrusionVector = new oc.gp_Vec_4(0, 0, -doorThickness)
          const prism = new oc.BRepPrimAPI_MakePrism_1(positionedProfile, extrusionVector, false, true)
          
          if (prism.IsDone()) {
            const extrudedProfile = prism.Shape()
            profileShapes.push(extrudedProfile)
            console.log(`✅ Created extruded profile at point ${i} (thickness: ${doorThickness}m)`)
          } else {
            // Fallback to just positioned profile
            profileShapes.push(positionedProfile)
            console.log(`⚠️ Extrusion failed for point ${i}, using positioned profile`)
          }
        } catch (extrusionError) {
          console.error(`❌ Extrusion error at point ${i}:`, extrusionError)
          // Fallback to just positioned profile
          profileShapes.push(positionedProfile)
          console.log(`⚠️ Using positioned profile as fallback for point ${i}`)
        }
      }
      
      // Also create intermediate profiles along each edge for smoother sweep
      console.log(`🔄 Adding intermediate profiles along edges...`)
      
      for (let i = 0; i < polylinePoints.length - 1; i++) {
        const startPoint = polylinePoints[i]
        const endPoint = polylinePoints[i + 1]
        
        // Add profiles at 25%, 50%, and 75% along each edge
        for (const t of [0.25, 0.5, 0.75]) {
          const interpX = startPoint.x + t * (endPoint.x - startPoint.x)
          const interpY = startPoint.y + t * (endPoint.y - startPoint.y)
          // Don't interpolate Z - use consistent depth through door body
          
          const tf = new oc.gp_Trsf_1()
          const penetrationStartZ = 0.001 // Start 1mm above door top surface
          tf.SetTranslation_1(new oc.gp_Vec_4(interpX, interpY, penetrationStartZ))
          
          const loc = new oc.TopLoc_Location_2(tf)
          const interpProfile = profileShape.Moved(loc, false)
          
          // Extrude down through door thickness
          try {
            const extrusionVector = new oc.gp_Vec_4(0, 0, -doorThickness)
            const prism = new oc.BRepPrimAPI_MakePrism_1(interpProfile, extrusionVector, false, true)
            
            if (prism.IsDone()) {
              const extrudedInterpProfile = prism.Shape()
              profileShapes.push(extrudedInterpProfile)
            } else {
              profileShapes.push(interpProfile)
            }
          } catch (extrusionError) {
            console.error(`❌ Intermediate extrusion error at edge ${i}, position ${t}:`, extrusionError)
            profileShapes.push(interpProfile)
          }
        }
      }
      
      // Fuse all profile shapes together to create swept geometry (OPTIMIZED)
      if (profileShapes.length === 0) {
        throw new Error('No profile shapes created')
      }

      console.log(`🔄 Optimized fusing of ${profileShapes.length} profile shapes...`)

      if (profileShapes.length === 1) {
        sweptShape = profileShapes[0]
      } else if (profileShapes.length <= 10) {
        // For small numbers, use sequential fuse
        sweptShape = profileShapes[0]

        for (let i = 1; i < profileShapes.length; i++) {
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            sweptShape,
            profileShapes[i],
            new oc.Message_ProgressRange_1()
          )
          fuse.Build(new oc.Message_ProgressRange_1())

          if (fuse.IsDone()) {
            sweptShape = fuse.Shape()
          } else {
            console.log(`⚠️ Fuse failed for profile ${i}, keeping previous shape`)
          }
        }
      } else {
        // For large numbers, use batch fuse approach (divide and conquer)
        console.log(`🔄 Using batch fuse for ${profileShapes.length} shapes...`)

        let currentShapes = [...profileShapes]

        while (currentShapes.length > 1) {
          const nextBatch: any[] = []
          const batchSize = Math.min(8, currentShapes.length) // Process in batches of 8

          for (let i = 0; i < currentShapes.length; i += batchSize) {
            const batch = currentShapes.slice(i, i + batchSize)

            if (batch.length === 1) {
              nextBatch.push(batch[0])
            } else {
              // Fuse this batch together
              let batchResult = batch[0]

              for (let j = 1; j < batch.length; j++) {
                const fuse = new oc.BRepAlgoAPI_Fuse_3(
                  batchResult,
                  batch[j],
                  new oc.Message_ProgressRange_1()
                )
                fuse.Build(new oc.Message_ProgressRange_1())

                if (fuse.IsDone()) {
                  batchResult = fuse.Shape()
                } else {
                  console.log(`⚠️ Batch fuse failed at position ${j}, keeping previous result`)
                }
              }

              nextBatch.push(batchResult)
            }
          }

          currentShapes = nextBatch
          console.log(`🔄 Batch fuse iteration complete, ${currentShapes.length} shapes remaining`)
        }

        sweptShape = currentShapes[0]
      }
      
      if (sweptShape.IsNull()) {
        throw new Error('Final fused shape is null')
      }
      
      console.log(`✅ Successfully created swept solid by placing profiles along polyline`)
      
      return sweptShape
      
    } catch (error) {
      console.error(`❌ Profile-based sweep error:`, error)
      
      // Final fallback: return the profile shape itself
      console.log(`🔄 Using profile shape as fallback...`)
      return profileShape
    }
    
  } catch (error) {
    console.error('❌ Error in createSweepFromPolyline:', error)
    throw error
  }
}

/**
 * Test function for polyline sweep operation
 * @param params Test parameters including polyline points and shapes
 * @returns Promise<any> The result of the sweep operation
 */
async function testPolylineSweep(params: TestPolylineSweepParams): Promise<any> {
  try {
    const { polylinePoints, profileShape, doorBodyShape } = params
    
    console.log('🧪 Testing polyline sweep operation')
    console.log(`📍 Polyline points: ${polylinePoints.length}`)
    console.log(`🔧 Profile shape: ${profileShape}`)
    console.log(`🚪 Door body shape: ${doorBodyShape}`)
    
    // Retrieve the profile shape from cache
    const profileShapeObj = shapeCache.get(profileShape)
    if (!profileShapeObj) {
      throw new Error(`Profile shape not found in cache: ${profileShape}`)
    }
    
    // Use the new real sweep function (BRepOffsetAPI_MakePipeShell)
    const sweptShape = await createRealSweepFromPolyline(oc, polylinePoints, profileShapeObj)
    
    // Cache the swept shape
    const sweptShapeId = `swept_${Date.now()}`
    shapeCache.set(sweptShapeId, sweptShape)
    
    console.log(`✅ Polyline sweep completed, cached as: ${sweptShapeId}`)
    
    // Get the door body shape from cache
    const doorBodyShapeObj = shapeCache.get(doorBodyShape)
    if (!doorBodyShapeObj) {
      throw new Error(`Door body shape not found in cache: ${doorBodyShape}`)
    }
    
    // Perform boolean subtraction to cut the swept shape from the door body
    console.log('🔄 Performing boolean subtraction...')
    const fuse = new oc.BRepAlgoAPI_Cut_3(
      doorBodyShapeObj,
      sweptShape,
      new oc.Message_ProgressRange_1()
    )
    fuse.Build(new oc.Message_ProgressRange_1())
    
    if (!fuse.IsDone()) {
      throw new Error('Boolean subtraction failed')
    }
    
    const finalShape = fuse.Shape()
    if (finalShape.IsNull()) {
      throw new Error('Boolean subtraction produced null shape')
    }
    
    // Cache the final result
    const finalShapeId = `polyline_sweep_result_${Date.now()}`
    shapeCache.set(finalShapeId, finalShape)
    
    console.log(`✅ Test completed successfully: ${finalShapeId}`)
    
    return {
      success: true,
      shapeId: finalShapeId,
      message: 'Polyline sweep test completed successfully'
    }
    
  } catch (error) {
    console.error('❌ Error in testPolylineSweep:', error)
    throw error
  }
}

// Create simple box GLB file from door parameters
function createSimpleBoxGLB(params: DoorBodyParams): any {
  try {
    console.log('Creating simple box GLB from door parameters')
    
    // Create the door body
    const doorResult = createDoorBody(params)
    if (!doorResult.success) {
      throw new Error('Failed to create door body for GLB export')
    }
    
    // Get the door shape from cache
    const doorShape = shapeCache.get(doorResult.shapeId)
    if (!doorShape) {
      throw new Error(`Door shape not found in cache: ${doorResult.shapeId}`)
    }
    
    // Export to GLB
    const glbData = exportToGLB(doorShape)
    
    console.log(`✅ Simple box GLB created: ${glbData.byteLength} bytes`)
    
    return {
      success: true,
      shapeId: doorResult.shapeId,
      glbData: glbData,
      dimensions: doorResult.dimensions
    }
  } catch (error) {
    console.error('Error creating simple box GLB:', error)
    throw new Error(`Failed to create simple box GLB: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
